<script setup lang="ts">
// 方案变更-用车方案
import { message } from 'ant-design-vue';
import { onMounted, ref, reactive, watch, nextTick, defineProps, defineEmits } from 'vue';

import { errorModal, resolveParam, routerParam, formatNumberThousands } from '@haierbusiness-front/utils';
import {
  SeatTypeConstant,
  CarBrandTypeConstant,
  CarUsageTypeConstant,
  UsageTimeTypeConstant,
  HotelsArr,
  VehiclesArr,
} from '@haierbusiness-front/common-libs';

const props = defineProps({
  schemeItem: {
    type: Object,
    default: {},
  },
  schemeCacheItem: {
    type: Object,
    default: {},
  },
  schemeChangeType: {
    // schemeBargainingEdit - 方案议价, schemeBargainingView - 议价查看, schemeChangeEdit - 方案变更, schemeChangeView - 方案变更查看, schemeWinBidView - 方案中标查看
    type: String,
    default: '',
  },
  isSchemeCache: {
    type: Boolean,
    default: false,
  },
  isSchemeBargain: {
    // 是否议价
    type: Boolean,
    default: false,
  },
  schemeIndex: {
    type: Number,
    default: 0,
  },
  processNode: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['schemePriceEmit', 'schemeVehiclesEmit']);

const oldSchemeList = ref<array>([]);
const newSchemeList = ref<array>([]);

const subtotal = ref<number>(0); // 小计

const isVerifyFailed = ref<boolean>(false); // 校验是否失败

// 用车
const vehiclesParams = ref<VehiclesArr>({
  demandDate: '', // 需求日期
  usageType: null, // 使用方式
  usageTime: null, // 使用时长
  seats: null, // 座位数
  schemeVehicleNum: null, // 车辆数量
  brand: null, // 品牌
  route: null, // 路线,多程逗号分隔
  routeList: [], // 路线

  schemeUnitPrice: null, // 自动测算单价

  description: '',
});

// 价格计算
const priceCalcFun = () => {
  const isAllPriceWrite = newSchemeList.value.every(
    (e) => e.schemeUnitPrice && e.schemeVehicleNum && e.schemeUnitPrice >= 0 && e.schemeVehicleNum >= 0,
  );
  subtotal.value = 0;

  if (isAllPriceWrite) {
    newSchemeList.value.forEach((e) => {
      subtotal.value += e.schemeUnitPrice * e.schemeVehicleNum;
    });

    emit('schemePriceEmit', { type: 'vehicle', totalPrice: subtotal.value, schemeIndex: props.schemeIndex });
  }
};

const schemePlanLabelList = ['用车方式', '车型', '型号', '用车数量', '使用时长', '路线', '路线概述', '备注'];

const changePrice = (index: number) => {
  // 价格计算
  priceCalcFun();
};

const changeUsageType = (index: number) => {
  newSchemeList.value[index].route = null;
  newSchemeList.value[index].usageTime = null;
};

const addScheme = (idx: number) => {
  isVerifyFailed.value = false;

  newSchemeList.value.push({
    isSchemeChangeAdd: true, // 是否变更方案新增

    ...vehiclesParams.value,

    demandDate: oldSchemeList.value[0]?.demandDate || '',
  });

  // priceCalcFun();
};
const delScheme = (idx: number) => {
  newSchemeList.value.splice(idx, 1);

  // priceCalcFun();
};

// 锚点
const anchorJump = (id: string) => {
  document.getElementById(id).scrollIntoView({ behavior: 'smooth', block: 'center' });
};

// 暂存
const vehicleTempSave = () => {
  emit('schemeVehiclesEmit', {
    schemeVehicles: [...newSchemeList.value],
    schemeIndex: props.schemeIndex,
  });
};

// 校验
const vehicleSub = () => {
  let isVerPassed = true;

  newSchemeList.value.forEach((e, i) => {
    isVerifyFailed.value = true;

    if (isVerPassed === false) return;

    if (e.usageType === null || e.usageType === undefined) {
      message.error('请选择' + e.demandDate + '用车' + (i + 1) + '用车方式');

      isVerPassed = false;
      anchorJump('schemeVehicleId' + e.demandDate + i);
      return;
    }

    if (e.seats === null || e.seats === undefined) {
      message.error('请选择' + e.demandDate + '用车' + (i + 1) + '车型');

      isVerPassed = false;
      anchorJump('schemeVehicleId' + e.demandDate + i);
      return;
    }

    if (e.brand === null || e.brand === undefined) {
      message.error('请选择' + e.demandDate + '用车' + (i + 1) + '型号');

      isVerPassed = false;
      anchorJump('schemeVehicleId' + e.demandDate + i);
      return;
    }

    if (e.schemeVehicleNum === null || e.schemeVehicleNum === undefined) {
      message.error('请填写' + e.demandDate + '用车' + (i + 1) + '用车数量');

      isVerPassed = false;
      anchorJump('schemeVehicleId' + e.demandDate + i);
      return;
    }

    if (e.usageType === 1 && (e.usageTime === null || e.usageTime === undefined)) {
      message.error('请选择' + e.demandDate + '用车' + (i + 1) + '使用时长');

      isVerPassed = false;
      anchorJump('schemeVehicleId' + e.demandDate + i);
      return;
    }

    if (!e.route) {
      message.error('请填写' + e.demandDate + '用车' + (i + 1) + (e.usageType === 1 ? '路线概述' : '路线'));

      isVerPassed = false;
      anchorJump('schemeVehicleId' + e.demandDate + i);
      return;
    }

    if (e.schemeUnitPrice === null || e.schemeUnitPrice === undefined) {
      message.error('请填写' + e.demandDate + '用车' + (i + 1) + '单价');

      isVerPassed = false;
      anchorJump('schemeVehicleId' + e.demandDate + i);
      return;
    }
  });

  if (isVerPassed) {
    vehicleTempSave();
  }

  return isVerPassed;
};

defineExpose({ vehicleSub, vehicleTempSave });

onMounted(async () => {
  if ((props.schemeItem && props.schemeItem.vehicles) || (props.schemeCacheItem && props.schemeCacheItem.vehicles)) {
    // console.log('%c [ 用车 ]-24', 'font-size:13px; background:pink; color:#bf2c9f;', props.schemeItem.vehicles);

    oldSchemeList.value = JSON.parse(JSON.stringify(props.schemeItem))?.vehicles || [];

    if (props.isSchemeCache && props.schemeCacheItem) {
      // 缓存 - 反显
      newSchemeList.value = props.schemeCacheItem?.vehicles || [];
    } else if (props.isSchemeBargain && props.schemeCacheItem) {
      // 议价、议价查看
      newSchemeList.value = props.schemeCacheItem?.vehicles || [];
    } else {
      // 变更查看
      newSchemeList.value = JSON.parse(JSON.stringify(oldSchemeList.value));
    }

    newSchemeList.value.forEach((e, index) => {
      // 议价
      if (props.isSchemeBargain) {
        e.schemeUnitPrice = e.schemeUnitPrice || oldSchemeList.value[index].schemeUnitPrice;
      }

      // 原价赋值
      e.oldSchemeUnitPrice = oldSchemeList.value[index]?.schemeUnitPrice || null;
    });

    // 价格计算
    priceCalcFun();
  }
});
</script>

<template>
  <!-- 用车方案 -->
  <div class="scheme_vehicle" v-if="oldSchemeList.length > 0 || newSchemeList.length > 0">
    <div class="common_table">
      <!-- 左侧 -->
      <div class="common_table_l">
        <div class="scheme_plan_title" v-show="oldSchemeList.length > 0">
          <div class="scheme_plan_img mr10"></div>
          <span>用车方案</span>
        </div>

        <div class="scheme_plan_table mt20" v-for="(item, idx) in oldSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '用车' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ CarUsageTypeConstant.ofType(item.usageType)?.desc || '-' }}
                </template>
                {{ CarUsageTypeConstant.ofType(item.usageType)?.desc || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ SeatTypeConstant.ofType(item.seats)?.desc || '-' }}
                </template>
                {{ SeatTypeConstant.ofType(item.seats)?.desc || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ CarBrandTypeConstant.ofType(item.brand)?.desc || '-' }}
                </template>
                {{ CarBrandTypeConstant.ofType(item.brand)?.desc || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.schemeVehicleNum ? item.schemeVehicleNum + '辆' : '-' }}
                </template>
                {{ item.schemeVehicleNum ? item.schemeVehicleNum + '辆' : '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.usageType == 1 ? UsageTimeTypeConstant.ofType(item.usageTime)?.desc || '-' : '-' }}
                </template>
                {{ item.usageType == 1 ? UsageTimeTypeConstant.ofType(item.usageTime)?.desc || '-' : '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.usageType == 0 ? item.route || '-' : '-' }}
                </template>
                <!-- 单趟 -->
                {{ item.usageType == 0 ? item.route || '-' : '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.usageType == 1 ? item.route || '-' : '-' }}
                </template>
                <!-- 包车 -->
                {{ item.usageType == 1 ? item.route || '-' : '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.description || '-' }}
                </template>
                {{ item.description || '-' }}
              </a-tooltip>
            </div>
          </div>
          <div class="scheme_plan_list3 pr12">
            <div class="scheme_plan_price">
              <div class="scheme_plan_price_label">单价：</div>
              <div class="scheme_plan_price_value">
                {{ '¥' + formatNumberThousands(item.schemeUnitPrice) }}
              </div>
            </div>
            <div class="scheme_plan_price mt36">
              <div class="scheme_plan_price_label">总额：</div>
              <div class="scheme_plan_price_value">
                {{
                  '¥' +
                  (item.schemeUnitPrice && item.schemeVehicleNum
                    ? formatNumberThousands(item.schemeUnitPrice * item.schemeVehicleNum)
                    : '0.00')
                }}
              </div>
            </div>
            <div class="scheme_plan_price_tip mt4">
              <div v-if="item.schemeUnitPrice">
                {{ item.schemeVehicleNum + '辆*' + item.schemeUnitPrice + '(单价)' }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="common_table_divide"></div>

      <!-- 右侧 -->
      <div class="common_table_r">
        <div class="scheme_plan_title" v-show="newSchemeList.length > 0">
          <div class="scheme_plan_img mr10"></div>
          <span>用车方案</span>
        </div>

        <div class="scheme_plan_table mt20" v-for="(item, idx) in newSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '用车' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value">
              <div
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed && (item.usageType === null || item.usageType === undefined) ? 'error_tip' : '',
                ]"
              >
                <div
                  class="pl12"
                  v-if="
                    ['schemeBargainingEdit', 'schemeBargainingView', 'schemeChangeView'].includes(schemeChangeType) ||
                    !item.isSchemeChangeAdd
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ CarUsageTypeConstant.ofType(item.usageType)?.desc || '-' }}
                    </template>
                    {{ CarUsageTypeConstant.ofType(item.usageType)?.desc || '-' }}
                  </a-tooltip>
                </div>

                <a-select
                  v-else
                  v-model:value="item.usageType"
                  @change="changeUsageType(idx)"
                  style="width: 100%"
                  placeholder="请选择用车方式"
                  :bordered="false"
                  :dropdownMatchSelectWidth="90"
                  allow-clear
                >
                  <a-select-option v-for="item in CarUsageTypeConstant.toArray()" :key="item.code" :value="item.code">
                    <a-tooltip placement="topLeft" :title="item.desc">
                      {{ item.desc }}
                    </a-tooltip>
                  </a-select-option>
                </a-select>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed && (item.seats === null || item.seats === undefined) ? 'error_tip' : '',
                ]"
              >
                <div
                  class="pl12"
                  v-if="
                    ['schemeBargainingEdit', 'schemeBargainingView', 'schemeChangeView'].includes(schemeChangeType) ||
                    !item.isSchemeChangeAdd
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ SeatTypeConstant.ofType(item.seats)?.desc || '-' }}
                    </template>
                    {{ SeatTypeConstant.ofType(item.seats)?.desc || '-' }}
                  </a-tooltip>
                </div>

                <a-select
                  v-else
                  v-model:value="item.seats"
                  style="width: 100%"
                  placeholder="请选择车型"
                  :bordered="false"
                  :dropdownMatchSelectWidth="90"
                  allow-clear
                >
                  <a-select-option v-for="item in SeatTypeConstant.toArray()" :key="item.code" :value="item.code">
                    <a-tooltip placement="topLeft" :title="item.desc">
                      {{ item.desc }}
                    </a-tooltip>
                  </a-select-option>
                </a-select>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed && (item.brand === null || item.brand === undefined) ? 'error_tip' : '',
                ]"
              >
                <div
                  class="pl12"
                  v-if="
                    ['schemeBargainingEdit', 'schemeBargainingView', 'schemeChangeView'].includes(schemeChangeType) ||
                    !item.isSchemeChangeAdd
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ CarBrandTypeConstant.ofType(item.brand)?.desc || '-' }}
                    </template>
                    {{ CarBrandTypeConstant.ofType(item.brand)?.desc || '-' }}
                  </a-tooltip>
                </div>

                <a-select
                  v-else
                  v-model:value="item.brand"
                  style="width: 100%"
                  placeholder="请选择型号"
                  :bordered="false"
                  :dropdownMatchSelectWidth="90"
                  allow-clear
                >
                  <a-select-option v-for="item in CarBrandTypeConstant.toArray()" :key="item.code" :value="item.code">
                    <a-tooltip placement="topLeft" :title="item.desc">
                      {{ item.desc }}
                    </a-tooltip>
                  </a-select-option>
                </a-select>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed && (item.schemeVehicleNum === null || item.schemeVehicleNum === undefined)
                    ? 'error_tip'
                    : '',
                ]"
              >
                <div
                  class="pl12"
                  v-if="
                    ['schemeBargainingView', 'schemeChangeView', 'schemeWinBidView'].includes(schemeChangeType) ||
                    !item.isSchemeChangeAdd
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ item.schemeVehicleNum ? item.schemeVehicleNum + '辆' : '-' }}
                    </template>
                    {{ item.schemeVehicleNum ? item.schemeVehicleNum + '辆' : '-' }}
                  </a-tooltip>
                </div>

                <div class="pl12" v-else>
                  <a-input-number
                    style="width: calc(100% - 44px)"
                    v-model:value="item.schemeVehicleNum"
                    @blur="changePrice(idx)"
                    placeholder="请填写用车数量"
                    :bordered="false"
                    allow-clear
                    :min="0"
                    :max="999999"
                    :precision="0"
                  />
                  <span>辆</span>
                  <div class="scheme_plan_edit"></div>
                </div>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed && (item.usageTime === null || item.usageTime === undefined) && item.usageType == 1
                    ? 'error_tip'
                    : '',
                ]"
              >
                <div
                  class="pl12"
                  v-if="
                    ['schemeBargainingEdit', 'schemeBargainingView', 'schemeChangeView'].includes(schemeChangeType) ||
                    !item.isSchemeChangeAdd ||
                    item.usageType !== 1
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ item.usageType == 1 ? UsageTimeTypeConstant.ofType(item.usageTime)?.desc || '-' : '-' }}
                    </template>
                    {{ item.usageType == 1 ? UsageTimeTypeConstant.ofType(item.usageTime)?.desc || '-' : '-' }}
                  </a-tooltip>
                </div>

                <a-select
                  v-else
                  v-model:value="item.usageTime"
                  style="width: 100%"
                  placeholder="请选择使用时长"
                  :bordered="false"
                  :dropdownMatchSelectWidth="90"
                  allow-clear
                >
                  <a-select-option v-for="item in UsageTimeTypeConstant.toArray()" :key="item.code" :value="item.code">
                    <a-tooltip placement="topLeft" :title="item.desc">
                      {{ item.desc }}
                    </a-tooltip>
                  </a-select-option>
                </a-select>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed && !item.route && item.usageType == 0 ? 'error_tip' : '',
                ]"
              >
                <div
                  class="pl12"
                  v-if="
                    ['schemeBargainingView', 'schemeChangeView', 'schemeWinBidView'].includes(schemeChangeType) ||
                    !item.isSchemeChangeAdd ||
                    item.usageType == 1
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ item.usageType == 0 ? item.route || '-' : '-' }}
                    </template>
                    {{ item.usageType == 0 ? item.route || '-' : '-' }}
                  </a-tooltip>
                </div>

                <div v-else>
                  <a-input
                    v-model:value="item.route"
                    style="width: calc(100% - 30px)"
                    placeholder="请填写路线"
                    :maxlength="500"
                    :bordered="false"
                    allow-clear
                  />
                  <div class="scheme_plan_edit"></div>
                </div>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed && !item.route && item.usageType == 1 ? 'error_tip' : '',
                ]"
              >
                <div
                  class="pl12"
                  v-if="
                    ['schemeBargainingView', 'schemeChangeView', 'schemeWinBidView'].includes(schemeChangeType) ||
                    !item.isSchemeChangeAdd ||
                    item.usageType == 0
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ item.usageType == 1 ? item.route || '-' : '-' }}
                    </template>
                    {{ item.usageType == 1 ? item.route || '-' : '-' }}
                  </a-tooltip>
                </div>

                <div v-else>
                  <a-input
                    v-model:value="item.route"
                    style="width: calc(100% - 30px)"
                    placeholder="请填写路线"
                    :maxlength="500"
                    :bordered="false"
                    allow-clear
                  />
                  <div class="scheme_plan_edit"></div>
                </div>
              </div>
            </div>
            <div class="scheme_plan_value p0">
              <div
                class="pl12"
                v-if="
                  ['schemeBargainingEdit', 'schemeBargainingView', 'schemeChangeView'].includes(schemeChangeType) ||
                  !item.isSchemeChangeAdd
                "
              >
                <a-tooltip placement="topLeft">
                  <template #title>
                    {{ item.description || '-' }}
                  </template>
                  {{ item.description || '-' }}
                </a-tooltip>
              </div>
              <a-tooltip placement="topLeft" v-else>
                <template #title v-if="item.description">
                  {{ item.description || '-' }}
                </template>
                <a-input
                  v-model:value="item.description"
                  style="width: calc(100% - 30px)"
                  placeholder="备注"
                  :maxlength="500"
                  :bordered="false"
                  allow-clear
                />
                <div class="scheme_plan_edit"></div>
              </a-tooltip>
            </div>
          </div>

          <div class="scheme_plan_list3 pr12" :id="'schemeVehicleId' + item.demandDate + idx">
            <div class="scheme_plan_price">
              <div class="scheme_plan_price_label">单价：</div>
              <div
                class="scheme_plan_price_value"
                v-if="
                  ['schemeBargainingView', 'schemeChangeView', 'schemeWinBidView'].includes(schemeChangeType) ||
                  (!item.isSchemeChangeAdd && schemeChangeType === 'schemeChangeEdit')
                "
              >
                {{ '¥' + formatNumberThousands(item.schemeUnitPrice) }}
              </div>
              <div
                :class="['scheme_plan_price_value', isVerifyFailed && !item.schemeUnitPrice ? 'error_price_tip' : '']"
                v-else
              >
                <a-input-number
                  v-model:value="item.schemeUnitPrice"
                  @change="changePrice(idx)"
                  placeholder=""
                  :bordered="false"
                  :controls="false"
                  :min="0"
                  :max="schemeChangeType === 'schemeBargainingEdit' ? item.oldSchemeUnitPrice : 999999.99"
                  :precision="2"
                  style="width: 100%"
                  allow-clear
                />
              </div>
            </div>
            <div class="scheme_plan_price mt36">
              <div class="scheme_plan_price_label">总额：</div>
              <div class="scheme_plan_price_value">
                {{
                  '¥' +
                  (item.schemeUnitPrice && item.schemeVehicleNum
                    ? formatNumberThousands(item.schemeUnitPrice * item.schemeVehicleNum)
                    : '0.00')
                }}
              </div>
            </div>
            <div class="scheme_plan_price_tip mt4">
              <div v-if="item.schemeUnitPrice">
                {{ item.schemeVehicleNum + '辆*' + item.schemeUnitPrice + '(单价)' }}
              </div>
            </div>

            <!-- 操作 -->
            <div
              class="action_icons"
              v-if="
                ['MICE_PENDING', 'MICE_EXECUTION', 'MICE_COMPLETED'].includes(processNode) &&
                schemeChangeType === 'schemeChangeEdit' &&
                newSchemeList.length > 1 &&
                item.isSchemeChangeAdd
              "
            >
              <a-popconfirm
                :title="'确认删除用车' + (idx + 1) + '？'"
                placement="topRight"
                ok-text="确认"
                cancel-text="取消"
                @confirm="delScheme(idx)"
              >
                <div class="del_icon"></div>
              </a-popconfirm>
            </div>
          </div>
        </div>

        <div v-show="subtotal" class="scheme_plan_subtotal mt16">
          {{ '小计：¥' + formatNumberThousands(subtotal) }}
        </div>

        <div
          v-if="
            ['MICE_PENDING', 'MICE_EXECUTION', 'MICE_COMPLETED'].includes(processNode) &&
            schemeChangeType === 'schemeChangeEdit'
          "
          class="add_scheme_plan mt20"
          @click="addScheme(index)"
        >
          <div class="plan_add_img mr8"></div>
          <span>新增用车</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.scheme_vehicle {
  .scheme_plan_img {
    background: url('@/assets/image/demand/demand_vehicle.png');
    background-repeat: no-repeat;
    background-size: 18px 18px;
    background-position: left center;
  }

  .scheme_plan_price_value {
    :deep(.ant-input-number .ant-input-number-input) {
      height: 24px;
      padding: 0 5px;
      text-align: end;

      width: 84px;
      font-weight: 500;
      font-size: 14px;
      color: #1868db;
      text-align: right;
      border-bottom: 1px solid #4e5969;
    }
  }

  .scheme_plan_value {
    :deep(.ant-input-number .ant-input-number-input) {
      height: 22px;
      padding: 0;
    }

    .scheme_plan_edit {
      margin-left: 5px;
      display: inline-flex;
      vertical-align: middle;

      width: 16px;
      height: 18px;
      background: url('@/assets/image/common/edit_gray.png');
      background-repeat: no-repeat;
      background-size: 16px 16px;
    }
  }

  .error_price_tip {
    :deep(.ant-input-number .ant-input-number-input) {
      border-bottom: 2px solid #ff4d4f;
    }
  }

  .p0 {
    padding: 0 !important;
  }
}
</style>
